import { PlusOutlined, SettingOutlined } from '@ant-design/icons';
import { Button, Typography } from 'antd';
import React from 'react';

import type { disclaimer } from 'src/api/disclaimer/disclaimer';
import type { globalAttribute } from 'src/api/globalAttribute/globalAttribute';
import type { uploadAdmin } from 'src/api/uploadAdmin/uploadAdmin';
import ConfigurableCard from 'src/components/ConfigurableCard';
import style from '../style.module.scss';
import AttributeRuleTable from './AttributeRuleTable';

const { Text } = Typography;

// 可复用的规则卡片组件
interface RuleCardProps {
  rule: disclaimer.IUpdateDisclaimerConditionRequest;
  index: number;
  allRules: disclaimer.IUpdateDisclaimerConditionRequest[];
  availableCategories: uploadAdmin.ICategory[];
  categoriesMap: Map<number, uploadAdmin.ICategory>;
  categoryAttributes: Map<number, globalAttribute.IGlobalAttr[]>;
  categoriesLoading: boolean;
  onUpdate: (conditionId: number, index: number, updates: Partial<disclaimer.IUpdateDisclaimerConditionRequest>) => void;
  onDelete: (conditionId: number, index: number) => void;
  onCategoryChange: (categoryId: number) => void;
  onEditRule: (rule: disclaimer.IUpdateDisclaimerConditionRequest) => void;
}

const RuleCard: React.FC<RuleCardProps> = ({
  rule,
  index,
  allRules,
  availableCategories,
  categoryAttributes,
  categoriesMap,
  categoriesLoading,
  onUpdate,
  onDelete,
  onEditRule,
}) => {
  // 处理 Attribute Rule 的增删改
  const handleAddAttributeRule = (attributeRule: disclaimer.IAttrCondition) => {
    const updatedRule = {
      ...rule,
      attrConditionInfo: {
        attrConditionList: [
          ...(rule.attrConditionInfo?.attrConditionList || []),
          attributeRule,
        ],
      },
    };
    onUpdate(rule.conditionId!, index, updatedRule);
  };

  const handleEditAttributeRule = (attrIndex: number, attributeRule: disclaimer.IAttrCondition) => {
    const updatedList = [...(rule.attrConditionInfo?.attrConditionList || [])];
    updatedList[attrIndex] = attributeRule;
    const updatedRule = {
      ...rule,
      attrConditionInfo: {
        attrConditionList: updatedList,
      },
    };
    onUpdate(rule.conditionId!, index, updatedRule);
  };

  const handleDeleteAttributeRule = (attrIndex: number) => {
    const updatedList = [...(rule.attrConditionInfo?.attrConditionList || [])];
    updatedList.splice(attrIndex, 1);
    const updatedRule = {
      ...rule,
      attrConditionInfo: {
        attrConditionList: updatedList,
      },
    };
    onUpdate(rule.conditionId!, index, updatedRule);
  };

  return (
    <ConfigurableCard
      key={rule.conditionId || index}
      header={{
        title: `Rule ${index + 1} — ${availableCategories.find(cat => cat.catId === rule.catId)?.catName || 'Unnamed Category'}`,
        extra: (
          <Button
            type="text"
            danger
            onClick={() => onDelete(rule.conditionId!, index)}
            style={{ color: '#ff4d4f' }}
          >
            Delete
          </Button>
        ),
      }}
      className={style.ruleCard}
    >
      <div className={style.ruleContent}>
        <div className={style.formField}>
          <Text strong>Associated Category</Text>
          <div style={{ marginTop: 8 }}>
            <div style={{ display: 'flex', alignItems: 'center', gap: 8 }}>
              <div style={{
                flex: 1,
                padding: '4px 11px',
                border: '1px solid #d9d9d9',
                borderRadius: '6px',
                backgroundColor: '#f5f5f5',
                minHeight: '32px',
                display: 'flex',
                alignItems: 'center'
              }}>
                <Text>
                  {categoriesMap.get(rule.catId!)?.catName || ''}
                </Text>
              </div>
              <Button
                type="default"
                size="small"
                onClick={() => onEditRule(rule)}
                disabled={categoriesLoading}
              >
                Edit
              </Button>
            </div>
          </div>
        </div>

        <AttributeRuleTable
          attributeRules={rule.attrConditionInfo?.attrConditionList || []}
          categoryAttributes={categoryAttributes.get(rule.catId!) || []}
          onAdd={handleAddAttributeRule}
          onEdit={handleEditAttributeRule}
          onDelete={handleDeleteAttributeRule}
        />
      </div>
    </ConfigurableCard>
  );
};

interface AdvancedSettingsProps {
  disclaimer?: disclaimer.IDisclaimer;
  advancedRules: disclaimer.IUpdateDisclaimerConditionRequest[];
  availableCategories: uploadAdmin.ICategory[];
  categoriesMap: Map<number, uploadAdmin.ICategory>;
  categoryAttributes: Map<number, globalAttribute.IGlobalAttr[]>;
  categoriesLoading: boolean;
  onAddRule: () => void;
  onUpdateRule: (conditionId: number, index: number, updates: Partial<disclaimer.IUpdateDisclaimerConditionRequest>) => void;
  onDeleteRule: (conditionId: number, index: number) => void;
  onCategoryChange: (categoryId: number) => void;
  onEditRule: (rule: disclaimer.IUpdateDisclaimerConditionRequest) => void;
}

const AdvancedSettings: React.FC<AdvancedSettingsProps> = ({
  disclaimer,
  advancedRules,
  availableCategories,
  categoriesMap,
  categoryAttributes,
  categoriesLoading,
  onAddRule,
  onUpdateRule,
  onDeleteRule,
  onCategoryChange,
  onEditRule,
}) => {
  return (
    <ConfigurableCard
      header={{
        title: 'Advanced Settings',
        icon: <SettingOutlined className={style.sectionIcon} />,
        description: <>Configure specific rules based on category + attribute conditions</>,
      }}
      collapsible={true}
      defaultCollapsed={false}
      className={style.sectionCard}
      onCollapse={onCollapse}
    >
      <div className={style.advancedContent}>
        {
          advancedRules.map((rule, index) => (
            <RuleCard
              key={rule.conditionId}
              rule={rule}
              index={index}
              allRules={advancedRules}
              availableCategories={availableCategories}
              categoriesMap={categoriesMap}
              categoryAttributes={categoryAttributes}
              categoriesLoading={categoriesLoading}
              onUpdate={onUpdateRule}
              onDelete={onDeleteRule}
              onCategoryChange={onCategoryChange}
              onEditRule={onEditRule}
            />
          ))
        }

        <Button
          type="primary"
          icon={<PlusOutlined />}
          onClick={onAddRule}
          style={{ marginTop: 16 }}
          disabled={!disclaimer?.id}
        >
          Add Another Rule
        </Button>
      </div>
    </ConfigurableCard>
  );
};

export default AdvancedSettings;
